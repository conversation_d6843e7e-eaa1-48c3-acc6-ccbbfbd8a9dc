/**
 * FullCalendar Width Optimization Utility
 * Ensures FullCalendar expands to fill available container width
 */

class FullCalendarWidthOptimizer {
    constructor(calendar, options = {}) {
        this.calendar = calendar;
        this.options = {
            resizeDelay: 250,
            forceResizeDelay: 1500,
            enableDebugLogging: false,
            ...options
        };
        this.resizeTimeout = null;
        this.init();
    }

    init() {
        this.log('Initializing FullCalendar Width Optimizer');
        this.setupResizeHandler();
        this.setupMutationObserver();
        this.scheduleForceResize();
    }

    log(message) {
        if (this.options.enableDebugLogging) {
            console.log(`[FullCalendar Width Optimizer] ${message}`);
        }
    }

    setupResizeHandler() {
        window.addEventListener('resize', () => {
            this.handleResize();
        });

        // Handle sidebar toggle events that might affect calendar width
        document.addEventListener('click', (e) => {
            if (e.target.closest('[data-bs-toggle="sidebar"]') ||
                e.target.closest('.sidebar-toggle') ||
                e.target.closest('.menu-toggle') ||
                e.target.closest('.app-sidebar-close') ||
                e.target.closest('.responsive-overlay')) {
                setTimeout(() => {
                    this.handleResize();
                }, 300); // Allow time for sidebar animation
            }
        });

        // Handle layout switcher changes
        document.addEventListener('change', (e) => {
            if (e.target.name === 'layout-width' ||
                e.target.name === 'layout-position' ||
                e.target.name === 'menu-positions') {
                setTimeout(() => {
                    this.handleResize();
                }, 500); // Allow time for layout changes
            }
        });
    }

    setupMutationObserver() {
        // Watch for changes in the calendar container or parent elements
        const observer = new MutationObserver((mutations) => {
            let shouldResize = false;
            
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && 
                    (mutation.attributeName === 'class' || 
                     mutation.attributeName === 'style')) {
                    shouldResize = true;
                }
            });

            if (shouldResize) {
                this.handleResize();
            }
        });

        // Observe the main content area and calendar container
        const mainContent = document.querySelector('.main-content');
        const calendarContainer = document.querySelector('#calendar');
        
        if (mainContent) {
            observer.observe(mainContent, {
                attributes: true,
                attributeFilter: ['class', 'style']
            });
        }

        if (calendarContainer) {
            observer.observe(calendarContainer.parentElement, {
                attributes: true,
                attributeFilter: ['class', 'style']
            });
        }
    }

    handleResize() {
        clearTimeout(this.resizeTimeout);
        this.resizeTimeout = setTimeout(() => {
            this.optimizeCalendarWidth();
        }, this.options.resizeDelay);
    }

    scheduleForceResize() {
        // Force resize after initial render to ensure full width
        setTimeout(() => {
            this.optimizeCalendarWidth();
        }, this.options.forceResizeDelay);
    }

    optimizeCalendarWidth() {
        if (!this.calendar) {
            this.log('Calendar instance not available');
            return;
        }

        try {
            this.log('Optimizing calendar dimensions');

            // Calculate optimal height
            this.calculateOptimalHeight();

            // Force calendar to recalculate its dimensions
            this.calendar.updateSize();

            // Additional width and height optimization
            this.forceWidthRecalculation();
            this.enforceEqualColumnWidths();

            this.log('Calendar dimension optimization completed');
        } catch (error) {
            console.error('Error optimizing calendar dimensions:', error);
        }
    }

    calculateOptimalHeight() {
        const calendarEl = document.querySelector('#calendar');
        if (!calendarEl) return;

        const header = document.querySelector('.app-header');
        const pageHeader = document.querySelector('.page-header-breadcrumb');
        const cardHeader = document.querySelector('.card-header');
        const legend = document.querySelector('.calendar-legend');

        let totalFixedHeight = 0;

        if (header) totalFixedHeight += header.offsetHeight;
        if (pageHeader) totalFixedHeight += pageHeader.offsetHeight;
        if (cardHeader) totalFixedHeight += cardHeader.offsetHeight;
        if (legend) totalFixedHeight += legend.offsetHeight;

        // Add padding and margins
        totalFixedHeight += 120; // Buffer for padding, margins, and other elements

        const optimalHeight = window.innerHeight - totalFixedHeight;
        const minHeight = 400;

        const finalHeight = Math.max(optimalHeight, minHeight);

        calendarEl.style.height = `${finalHeight}px`;

        this.log(`Calculated optimal height: ${finalHeight}px`);
    }

    enforceEqualColumnWidths() {
        const calendarEl = document.querySelector('#calendar');
        if (!calendarEl) return;

        // Force equal column widths by adding colgroup if not present
        const tables = calendarEl.querySelectorAll('.fc-scrollgrid table');

        tables.forEach(table => {
            let colgroup = table.querySelector('colgroup');
            if (!colgroup) {
                colgroup = document.createElement('colgroup');
                table.insertBefore(colgroup, table.firstChild);
            }

            // Clear existing cols
            colgroup.innerHTML = '';

            // Add 7 equal-width columns for days of the week
            for (let i = 0; i < 7; i++) {
                const col = document.createElement('col');
                col.style.width = '14.2857%';
                colgroup.appendChild(col);
            }
        });

        this.log('Enforced equal column widths');
    }

    forceWidthRecalculation() {
        const calendarEl = document.querySelector('#calendar');
        if (!calendarEl) return;

        // Temporarily hide and show to force recalculation
        const originalDisplay = calendarEl.style.display;
        calendarEl.style.display = 'none';
        
        // Force reflow
        calendarEl.offsetHeight;
        
        calendarEl.style.display = originalDisplay;
        
        // Apply width optimization classes
        calendarEl.classList.add('fc-calendar-force-resize');
        
        // Remove the class after a short delay
        setTimeout(() => {
            calendarEl.classList.remove('fc-calendar-force-resize');
        }, 100);
    }

    // Method to manually trigger optimization
    optimize() {
        this.optimizeCalendarWidth();
    }

    // Method to initialize calendar with optimal settings
    initializeCalendarDimensions() {
        this.log('Initializing calendar dimensions');

        // Set up initial height calculation
        this.calculateOptimalHeight();

        // Set up equal column widths
        setTimeout(() => {
            this.enforceEqualColumnWidths();
        }, 100);

        // Force calendar update
        setTimeout(() => {
            if (this.calendar) {
                this.calendar.updateSize();
            }
        }, 200);
    }

    // Method to destroy the optimizer
    destroy() {
        if (this.resizeTimeout) {
            clearTimeout(this.resizeTimeout);
        }
        window.removeEventListener('resize', this.handleResize);
        this.log('FullCalendar Width Optimizer destroyed');
    }
}

// Auto-initialize for calendars with specific ID
document.addEventListener('DOMContentLoaded', function() {
    // Wait for FullCalendar to be initialized
    setTimeout(() => {
        const calendarEl = document.querySelector('#calendar');
        if (calendarEl && window.calendar) {
            window.calendarWidthOptimizer = new FullCalendarWidthOptimizer(window.calendar, {
                enableDebugLogging: false // Set to true for debugging
            });
        }
    }, 2000);
});

// Export for manual use
window.FullCalendarWidthOptimizer = FullCalendarWidthOptimizer;
