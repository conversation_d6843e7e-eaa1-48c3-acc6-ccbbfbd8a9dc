/* FullCalendar Width Expansion and Height Optimization */

/* Core width expansion fixes */
.fc {
    width: 100% !important;
    max-width: none !important;
    min-width: 0 !important;
    height: 100% !important;
}

.fc .fc-scrollgrid,
.fc .fc-scrollgrid table {
    width: 100% !important;
    max-width: none !important;
    min-width: 0 !important;
    table-layout: fixed !important; /* Fixed for equal column widths */
}

/* Equal Column Width Enforcement */
.fc-daygrid-day,
.fc-col-header-cell,
.fc-timegrid-col {
    width: 14.2857% !important; /* 100% / 7 days = 14.2857% */
    min-width: 0 !important;
    max-width: none !important;
}

/* Ensure equal widths in time grid views */
.fc-timegrid-slots .fc-timegrid-slot,
.fc-timegrid-axis {
    width: auto !important;
}

.fc-timegrid-col-frame {
    width: 100% !important;
}

/* Force equal column distribution */
.fc-scrollgrid-section table {
    table-layout: fixed !important;
}

.fc-scrollgrid-section table colgroup col {
    width: 14.2857% !important;
}

/* Height optimization for full container utilization */
.fc-view-harness {
    height: 100% !important;
    min-height: 0 !important;
}

.fc-view {
    height: 100% !important;
    min-height: 0 !important;
}

/* Calendar container optimization with height management */
#calendar {
    width: 100% !important;
    max-width: none !important;
    min-width: 0 !important;
    height: calc(100vh - 280px) !important; /* Adjust based on header/toolbar height */
    min-height: 500px !important;
    overflow-x: auto !important;
    overflow-y: auto !important;
}

/* Dynamic height calculation for different layouts */
[data-nav-layout="horizontal"] #calendar {
    height: calc(100vh - 320px) !important;
}

[data-nav-layout="vertical"] #calendar {
    height: calc(100vh - 280px) !important;
}

/* Card body height optimization */
.card.custom-card .card-body {
    height: calc(100vh - 220px) !important;
    min-height: 600px !important;
    display: flex !important;
    flex-direction: column !important;
}

/* Calendar takes remaining space in card body */
.card.custom-card .card-body #calendar {
    flex: 1 1 auto !important;
    height: auto !important;
    min-height: 500px !important;
}

/* Override container width constraints for calendar pages */
.calendar-container-override {
    width: 100% !important;
    max-width: none !important;
    min-width: 0 !important;
}

/* Layout-specific optimizations */
[data-nav-layout="horizontal"] .app-content > .container-fluid {
    width: 98% !important;
    max-width: none !important;
}

[data-nav-layout="vertical"] .app-content > .container-fluid {
    width: 100% !important;
    max-width: none !important;
}

/* Card container optimization */
.card.custom-card {
    width: 100% !important;
    max-width: none !important;
    min-width: 0 !important;
}

.card.custom-card .card-body {
    width: 100% !important;
    max-width: none !important;
    min-width: 0 !important;
    overflow-x: auto !important;
}

/* FullCalendar toolbar responsive improvements */
.fc-toolbar {
    flex-wrap: wrap !important;
    width: 100% !important;
    max-width: none !important;
}

.fc-toolbar-chunk {
    flex-shrink: 0 !important;
}

/* FullCalendar table cell optimization with equal widths */
.fc-daygrid-day-frame {
    min-width: 0 !important;
    width: 100% !important;
    height: 100% !important;
}

.fc-col-header-cell {
    min-width: 0 !important;
    width: 14.2857% !important;
    text-align: center !important;
}

/* Day grid body optimization for equal heights */
.fc-daygrid-body {
    height: 100% !important;
}

.fc-daygrid-day {
    height: auto !important;
    min-height: 120px !important;
}

/* Time grid column optimization */
.fc-timegrid-col {
    width: 14.2857% !important;
    min-width: 0 !important;
}

/* Ensure consistent column spacing */
.fc-scrollgrid-section-body table,
.fc-scrollgrid-section-header table {
    width: 100% !important;
    table-layout: fixed !important;
}

/* Column header optimization */
.fc-col-header-cell-cushion {
    padding: 8px 4px !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
}

/* Event display optimization for wider calendars */
.fc-daygrid-event {
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
}

.fc-daygrid-event .fc-event-title {
    overflow: hidden !important;
    text-overflow: ellipsis !important;
}

/* Responsive breakpoints for calendar optimization with height adjustments */
@media (min-width: 1400px) {
    .fc {
        font-size: 1em !important;
    }

    .fc-daygrid-event {
        font-size: 0.9em !important;
    }

    .fc-daygrid-day {
        min-height: 140px !important;
    }

    #calendar {
        height: calc(100vh - 260px) !important;
    }
}

@media (min-width: 1600px) {
    .fc {
        font-size: 1.05em !important;
    }

    .fc-daygrid-event {
        font-size: 0.95em !important;
    }

    .fc-daygrid-day {
        min-height: 160px !important;
    }

    #calendar {
        height: calc(100vh - 240px) !important;
    }
}

@media (min-width: 1920px) {
    .fc {
        font-size: 1.1em !important;
    }

    .fc-daygrid-event {
        font-size: 1em !important;
    }

    .fc-daygrid-day {
        min-height: 180px !important;
    }

    #calendar {
        height: calc(100vh - 220px) !important;
    }
}

/* Mobile responsive improvements with height optimization */
@media (max-width: 768px) {
    .fc-toolbar {
        flex-direction: column !important;
        gap: 0.5rem !important;
    }

    .fc-toolbar-chunk {
        justify-content: center !important;
        width: 100% !important;
    }

    .fc-button-group {
        width: 100% !important;
        justify-content: center !important;
    }

    #calendar {
        height: calc(100vh - 350px) !important;
        min-height: 400px !important;
        overflow-x: auto !important;
        -webkit-overflow-scrolling: touch !important;
    }

    .fc-daygrid-day {
        min-height: 80px !important;
    }

    .card.custom-card .card-body {
        height: calc(100vh - 180px) !important;
        min-height: 500px !important;
    }
}

@media (max-width: 576px) {
    .fc-toolbar-title {
        font-size: 1.25em !important;
    }

    .fc-button {
        padding: 0.25rem 0.5rem !important;
        font-size: 0.875rem !important;
    }

    #calendar {
        height: calc(100vh - 380px) !important;
        min-height: 350px !important;
    }

    .fc-daygrid-day {
        min-height: 60px !important;
    }

    .fc-col-header-cell-cushion {
        padding: 4px 2px !important;
        font-size: 0.8em !important;
    }
}

/* Ensure calendar takes full width in all Bootstrap grid contexts */
.row .col-xl-12 #calendar,
.row .col-lg-12 #calendar,
.row .col-md-12 #calendar,
.row .col-12 #calendar {
    width: 100% !important;
    max-width: none !important;
}

/* Fix for potential flex container issues */
.fc-view-harness {
    width: 100% !important;
    max-width: none !important;
    min-width: 0 !important;
}

.fc-view {
    width: 100% !important;
    max-width: none !important;
    min-width: 0 !important;
}

/* Scrollgrid liquid layout optimization */
.fc-scrollgrid-liquid {
    width: 100% !important;
    max-width: none !important;
}

/* Time grid specific optimizations */
.fc-timegrid {
    width: 100% !important;
    max-width: none !important;
}

.fc-timegrid-cols {
    width: 100% !important;
    max-width: none !important;
}

/* List view optimization */
.fc-list {
    width: 100% !important;
    max-width: none !important;
}

.fc-list-table {
    width: 100% !important;
    max-width: none !important;
    table-layout: auto !important;
}

/* Prevent calendar from being constrained by parent elements */
.fc * {
    box-sizing: border-box !important;
}

/* Force recalculation of calendar dimensions */
.fc-calendar-force-resize {
    width: 100% !important;
    max-width: none !important;
    min-width: 0 !important;
    display: block !important;
}

/* Additional safety measures for width expansion */
.main-content .container-fluid .row .col-xl-12 .card .card-body #calendar {
    width: 100% !important;
    max-width: none !important;
    min-width: 0 !important;
    flex: 1 1 auto !important;
}

/* Force equal column widths in all scenarios */
.fc-scrollgrid colgroup {
    width: 100% !important;
}

.fc-scrollgrid colgroup col {
    width: 14.2857% !important;
    min-width: 0 !important;
    max-width: none !important;
}

/* Ensure day grid cells maintain equal width */
.fc-daygrid-day-top,
.fc-daygrid-day-events,
.fc-daygrid-day-bg {
    width: 100% !important;
}

/* Time grid specific equal width enforcement */
.fc-timegrid-col-bg,
.fc-timegrid-col-events,
.fc-timegrid-col-frame {
    width: 100% !important;
}

/* List view equal width columns */
.fc-list-table td {
    width: auto !important;
}

.fc-list-table .fc-list-event-graphic {
    width: 20px !important;
    min-width: 20px !important;
}

.fc-list-table .fc-list-event-time {
    width: 100px !important;
    min-width: 100px !important;
}

/* Prevent content from breaking equal widths */
.fc-daygrid-event-harness,
.fc-daygrid-event {
    max-width: 100% !important;
    overflow: hidden !important;
}

/* Header cell content optimization */
.fc-col-header-cell-cushion {
    width: 100% !important;
    box-sizing: border-box !important;
}

/* Day number positioning */
.fc-daygrid-day-number {
    width: 100% !important;
    text-align: right !important;
    padding-right: 8px !important;
}

/* Week number column width */
.fc-daygrid-week-number {
    width: 40px !important;
    min-width: 40px !important;
    max-width: 40px !important;
}

/* Adjust main grid when week numbers are shown */
.fc-scrollgrid-section table:has(.fc-daygrid-week-number) colgroup col:not(:first-child) {
    width: calc((100% - 40px) / 7) !important;
}

/* Time axis width in time grid views */
.fc-timegrid-axis {
    width: 60px !important;
    min-width: 60px !important;
    max-width: 60px !important;
}

/* Adjust time grid columns when axis is present */
.fc-timegrid-body colgroup col {
    width: calc((100% - 60px) / 7) !important;
}
