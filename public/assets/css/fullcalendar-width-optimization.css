/* FullCalendar Width Expansion Optimization */

/* Core width expansion fixes */
.fc {
    width: 100% !important;
    max-width: none !important;
    min-width: 0 !important;
}

.fc .fc-scrollgrid,
.fc .fc-scrollgrid table {
    width: 100% !important;
    max-width: none !important;
    min-width: 0 !important;
    table-layout: auto !important;
}

/* Calendar container optimization */
#calendar {
    width: 100% !important;
    max-width: none !important;
    min-width: 0 !important;
    overflow-x: auto !important;
}

/* Override container width constraints for calendar pages */
.calendar-container-override {
    width: 100% !important;
    max-width: none !important;
    min-width: 0 !important;
}

/* Layout-specific optimizations */
[data-nav-layout="horizontal"] .app-content > .container-fluid {
    width: 98% !important;
    max-width: none !important;
}

[data-nav-layout="vertical"] .app-content > .container-fluid {
    width: 100% !important;
    max-width: none !important;
}

/* Card container optimization */
.card.custom-card {
    width: 100% !important;
    max-width: none !important;
    min-width: 0 !important;
}

.card.custom-card .card-body {
    width: 100% !important;
    max-width: none !important;
    min-width: 0 !important;
    overflow-x: auto !important;
}

/* FullCalendar toolbar responsive improvements */
.fc-toolbar {
    flex-wrap: wrap !important;
    width: 100% !important;
    max-width: none !important;
}

.fc-toolbar-chunk {
    flex-shrink: 0 !important;
}

/* FullCalendar table cell optimization */
.fc-daygrid-day-frame {
    min-width: 0 !important;
}

.fc-col-header-cell {
    min-width: 0 !important;
}

/* Event display optimization for wider calendars */
.fc-daygrid-event {
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
}

.fc-daygrid-event .fc-event-title {
    overflow: hidden !important;
    text-overflow: ellipsis !important;
}

/* Responsive breakpoints for calendar optimization */
@media (min-width: 1400px) {
    .fc {
        font-size: 1em !important;
    }
    
    .fc-daygrid-event {
        font-size: 0.9em !important;
    }
}

@media (min-width: 1600px) {
    .fc {
        font-size: 1.05em !important;
    }
    
    .fc-daygrid-event {
        font-size: 0.95em !important;
    }
}

@media (min-width: 1920px) {
    .fc {
        font-size: 1.1em !important;
    }
    
    .fc-daygrid-event {
        font-size: 1em !important;
    }
}

/* Mobile responsive improvements */
@media (max-width: 768px) {
    .fc-toolbar {
        flex-direction: column !important;
        gap: 0.5rem !important;
    }
    
    .fc-toolbar-chunk {
        justify-content: center !important;
        width: 100% !important;
    }
    
    .fc-button-group {
        width: 100% !important;
        justify-content: center !important;
    }
    
    #calendar {
        overflow-x: auto !important;
        -webkit-overflow-scrolling: touch !important;
    }
}

@media (max-width: 576px) {
    .fc-toolbar-title {
        font-size: 1.25em !important;
    }
    
    .fc-button {
        padding: 0.25rem 0.5rem !important;
        font-size: 0.875rem !important;
    }
}

/* Ensure calendar takes full width in all Bootstrap grid contexts */
.row .col-xl-12 #calendar,
.row .col-lg-12 #calendar,
.row .col-md-12 #calendar,
.row .col-12 #calendar {
    width: 100% !important;
    max-width: none !important;
}

/* Fix for potential flex container issues */
.fc-view-harness {
    width: 100% !important;
    max-width: none !important;
    min-width: 0 !important;
}

.fc-view {
    width: 100% !important;
    max-width: none !important;
    min-width: 0 !important;
}

/* Scrollgrid liquid layout optimization */
.fc-scrollgrid-liquid {
    width: 100% !important;
    max-width: none !important;
}

/* Time grid specific optimizations */
.fc-timegrid {
    width: 100% !important;
    max-width: none !important;
}

.fc-timegrid-cols {
    width: 100% !important;
    max-width: none !important;
}

/* List view optimization */
.fc-list {
    width: 100% !important;
    max-width: none !important;
}

.fc-list-table {
    width: 100% !important;
    max-width: none !important;
    table-layout: auto !important;
}

/* Prevent calendar from being constrained by parent elements */
.fc * {
    box-sizing: border-box !important;
}

/* Force recalculation of calendar dimensions */
.fc-calendar-force-resize {
    width: 100% !important;
    max-width: none !important;
    min-width: 0 !important;
    display: block !important;
}

/* Additional safety measures for width expansion */
.main-content .container-fluid .row .col-xl-12 .card .card-body #calendar {
    width: 100% !important;
    max-width: none !important;
    min-width: 0 !important;
    flex: 1 1 auto !important;
}
